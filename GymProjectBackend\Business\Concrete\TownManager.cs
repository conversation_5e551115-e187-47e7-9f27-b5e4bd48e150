using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Core.Aspects.Autofac.Caching;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class TownManager : ITownService
    {
        ITownDal _townDal;

        public TownManager(ITownDal townDal)
        {
            _townDal = townDal;
        }
        [CacheAspect(duration: 86400)] // Cold data - 24 saat (ilçeler çok nadir değişir)
        [SecuredOperation("owner,admin")]
        public IDataResult<List<Town>> GetAll()
        {
            return new SuccessDataResult<List<Town>>(_townDal.GetAll());

        }
        [CacheAspect(duration: 86400)] // Cold data - 24 saat (ilçeler çok nadir değişir)
        [SecuredOperation("owner,admin")]
        public IDataResult<List<Town>> GetByCityId(int cityId)
        {
            return new SuccessDataResult<List<Town>>(_townDal.GetAll(c => c.CityID == cityId));
        }
    }
}
