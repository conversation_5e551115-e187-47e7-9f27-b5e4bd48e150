using Castle.DynamicProxy;
using Core.CrossCuttingConcerns.Caching;
using Core.Utilities.Interceptors;
using Core.Utilities.IoC;
using Core.Utilities.Security.CompanyContext;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;

namespace Core.Aspects.Autofac.Caching
{
    /// <summary>
    /// Cache Remove Aspect - Spesifik method cache'lerini temizle
    /// Multi-tenant yapıya uygun, method-specific cache removal
    /// 
    /// Kullanım:
    /// [CacheRemoveAspect("GetMemberById")] // Tek method
    /// [CacheRemoveAspect("GetMemberById", "GetAllMembers")] // Çoklu method
    /// [CacheRemoveAspect("GetMember*")] // Pattern-based
    /// </summary>
    public class CacheRemoveAspect : MethodInterception
    {
        #region Fields

        private readonly string[] _methodPatterns;
        private readonly bool _removeRelatedMethods;
        private readonly bool _enableLogging;
        private readonly ICacheService _cacheService;
        private readonly ICompanyContext _companyContext;
        private readonly ILogger<CacheRemoveAspect> _logger;

        #endregion

        #region Constructor

        /// <summary>
        /// Cache Remove Aspect Constructor
        /// </summary>
        /// <param name="methodPatterns">Temizlenecek method pattern'leri</param>
        public CacheRemoveAspect(params string[] methodPatterns)
            : this(true, true, methodPatterns)
        {
        }

        /// <summary>
        /// Cache Remove Aspect Constructor - Advanced
        /// </summary>
        /// <param name="removeRelatedMethods">İlgili method'ları da temizle</param>
        /// <param name="enableLogging">Logging aktif mi</param>
        /// <param name="methodPatterns">Temizlenecek method pattern'leri</param>
        public CacheRemoveAspect(bool removeRelatedMethods, bool enableLogging, params string[] methodPatterns)
        {
            _methodPatterns = methodPatterns ?? new string[0];
            _removeRelatedMethods = removeRelatedMethods;
            _enableLogging = enableLogging;

            // Dependency injection
            _cacheService = ServiceTool.ServiceProvider.GetService<ICacheService>();
            _companyContext = ServiceTool.ServiceProvider.GetService<ICompanyContext>();
            _logger = ServiceTool.ServiceProvider.GetService<ILogger<CacheRemoveAspect>>();
        }

        #endregion

        #region Method Interception

        protected override void OnAfter(IInvocation invocation)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                {
                    _logger.LogWarning("Invalid company ID for cache removal: {CompanyId}", companyId);
                    return;
                }

                var removedCount = 0;
                var className = invocation.TargetType.Name;

                foreach (var methodPattern in _methodPatterns)
                {
                    removedCount += RemoveMethodCache(companyId, className, methodPattern);
                }

                if (_enableLogging && removedCount > 0)
                {
                    _logger.LogDebug("Cache removal completed: {Method} - {Count} keys removed for patterns: {Patterns}", 
                        GetMethodSignature(invocation), removedCount, string.Join(", ", _methodPatterns));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Cache removal error for method: {Method}", GetMethodSignature(invocation));
            }
        }

        #endregion

        #region Cache Removal Methods

        /// <summary>
        /// Method cache'lerini pattern'e göre temizle
        /// </summary>
        private int RemoveMethodCache(int companyId, string className, string methodPattern)
        {
            var removedCount = 0;

            try
            {
                // Method pattern'ini oluştur
                var cachePattern = GenerateCachePattern(companyId, className, methodPattern);
                
                // Pattern'e uyan cache key'leri bul
                var cacheKeys = _cacheService.GetKeys(cachePattern);
                
                if (cacheKeys?.Any() == true)
                {
                    removedCount = (int)_cacheService.RemoveMultiple(cacheKeys);
                    
                    if (_enableLogging && removedCount > 0)
                    {
                        _logger.LogDebug("Method cache removed: {Pattern} - {Count} keys removed", 
                            cachePattern, removedCount);
                    }
                }

                // İlgili method'ları da temizle (opsiyonel)
                if (_removeRelatedMethods)
                {
                    removedCount += RemoveRelatedMethodCache(companyId, className, methodPattern);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing method cache for pattern: {Pattern}", methodPattern);
            }

            return removedCount;
        }

        /// <summary>
        /// İlgili method cache'lerini temizle
        /// Örnek: GetMemberById temizlendiğinde GetAllMembers, GetMembersByCompany vb. de temizlenir
        /// </summary>
        private int RemoveRelatedMethodCache(int companyId, string className, string methodPattern)
        {
            var removedCount = 0;

            try
            {
                // Method pattern'inden entity adını çıkar
                var entityName = ExtractEntityNameFromMethod(methodPattern);
                
                if (!string.IsNullOrEmpty(entityName))
                {
                    // Entity ile ilgili tüm method cache'lerini temizle
                    var relatedPattern = $"gym:{companyId}:method:{className}:*{entityName}*";
                    var relatedKeys = _cacheService.GetKeys(relatedPattern);
                    
                    if (relatedKeys?.Any() == true)
                    {
                        removedCount = (int)_cacheService.RemoveMultiple(relatedKeys);
                        
                        if (_enableLogging && removedCount > 0)
                        {
                            _logger.LogDebug("Related method cache removed: {Pattern} - {Count} keys removed", 
                                relatedPattern, removedCount);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing related method cache for pattern: {Pattern}", methodPattern);
            }

            return removedCount;
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Cache pattern oluştur
        /// </summary>
        private string GenerateCachePattern(int companyId, string className, string methodPattern)
        {
            // Wildcard pattern'leri destekle
            if (methodPattern.Contains("*"))
            {
                return $"gym:{companyId}:method:{className}:{methodPattern}*";
            }
            else
            {
                return $"gym:{companyId}:method:{className}:{methodPattern}:*";
            }
        }

        /// <summary>
        /// Method adından entity adını çıkar
        /// Örnek: GetMemberById -> Member, GetAllMembers -> Member
        /// </summary>
        private string ExtractEntityNameFromMethod(string methodName)
        {
            try
            {
                // Yaygın method prefix'lerini temizle
                var prefixes = new[] { "Get", "GetAll", "GetBy", "Add", "Update", "Delete", "Remove" };
                var cleanMethodName = methodName;

                foreach (var prefix in prefixes)
                {
                    if (cleanMethodName.StartsWith(prefix, StringComparison.OrdinalIgnoreCase))
                    {
                        cleanMethodName = cleanMethodName.Substring(prefix.Length);
                        break;
                    }
                }

                // Suffix'leri temizle
                var suffixes = new[] { "s", "ById", "ByCompany", "ByUser", "List", "Paginated" };
                foreach (var suffix in suffixes)
                {
                    if (cleanMethodName.EndsWith(suffix, StringComparison.OrdinalIgnoreCase))
                    {
                        cleanMethodName = cleanMethodName.Substring(0, cleanMethodName.Length - suffix.Length);
                        break;
                    }
                }

                return cleanMethodName;
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// Method signature'ını al (logging için)
        /// </summary>
        private string GetMethodSignature(IInvocation invocation)
        {
            return $"{invocation.TargetType.Name}.{invocation.Method.Name}";
        }

        #endregion
    }
}
